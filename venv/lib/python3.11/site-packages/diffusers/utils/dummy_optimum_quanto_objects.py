# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class QuantoConfig(metaclass=DummyObject):
    _backends = ["optimum_quanto"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["optimum_quanto"])

    @classmethod
    def from_config(cls, *args, **kwargs):
        requires_backends(cls, ["optimum_quanto"])

    @classmethod
    def from_pretrained(cls, *args, **kwargs):
        requires_backends(cls, ["optimum_quanto"])
