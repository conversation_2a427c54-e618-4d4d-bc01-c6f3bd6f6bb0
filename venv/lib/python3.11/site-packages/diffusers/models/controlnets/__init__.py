from ...utils import is_flax_available, is_torch_available


if is_torch_available():
    from .controlnet import ControlNetModel, ControlNetOutput
    from .controlnet_flux import FluxControlNetModel, FluxControlNetOutput, FluxMultiControlNetModel
    from .controlnet_hunyuan import (
        HunyuanControlNetOutput,
        HunyuanDiT2DControlNetModel,
        HunyuanDiT2DMultiControlNetModel,
    )
    from .controlnet_sd3 import SD3ControlNetModel, SD3ControlNetOutput, SD3MultiControlNetModel
    from .controlnet_sparsectrl import (
        SparseControlNetConditioningEmbedding,
        SparseControlNetModel,
        SparseControlNetOutput,
    )
    from .controlnet_union import ControlNetUnionModel
    from .controlnet_xs import ControlNetXSAdapter, ControlNetXSOutput, UNetControlNetXSModel
    from .multicontrolnet import MultiControlNetModel
    from .multicontrolnet_union import MultiControlNetUnionModel

if is_flax_available():
    from .controlnet_flax import FlaxControlNetModel
