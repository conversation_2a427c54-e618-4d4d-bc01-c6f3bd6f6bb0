#!/usr/bin/env python3
"""
Multi-Character LoRA Training Pipeline for SDXL
Designed for RunPod environments
"""

import os
import json
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from transformers import CLIPTextModel, CLIPTokenizer, CLIPTextModelWithProjection
from diffusers import UNet2DConditionModel, AutoencoderKL
from diffusers.optimization import get_scheduler
from diffusers.utils import check_min_version
from PIL import Image
import numpy as np
from pathlib import Path
import argparse
import logging
from tqdm import tqdm
import math
from typing import Dict, List, Tuple, Optional
import random
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import set_seed

# Check diffusers version
check_min_version("0.21.0")

# Setup logging
logger = get_logger(__name__)

class LoRALinearLayer(torch.nn.Module):
    """LoRA Linear Layer implementation"""
    def __init__(self, in_features: int, out_features: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        
        # LoRA matrices
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_features) * 0.01)
        self.lora_B = torch.nn.Parameter(torch.zeros(out_features, rank))
        
    def forward(self, x):
        # Ensure consistent dtype
        lora_A = self.lora_A.to(x.dtype)
        lora_B = self.lora_B.to(x.dtype)
        return (x @ lora_A.T @ lora_B.T) * self.scaling

class LoRAConv2d(torch.nn.Module):
    """LoRA Convolutional Layer implementation"""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        self.kernel_size = kernel_size
        
        # LoRA matrices for conv layers
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_channels, kernel_size, kernel_size) * 0.01)
        self.lora_B = torch.nn.Parameter(torch.zeros(out_channels, rank, 1, 1))
        
    def forward(self, x):
        # Ensure consistent dtype
        lora_A = self.lora_A.to(x.dtype)
        lora_B = self.lora_B.to(x.dtype)
        return F.conv2d(x, (lora_B.squeeze() @ lora_A.view(self.rank, -1)).view(
            lora_B.shape[0], lora_A.shape[1], self.kernel_size, self.kernel_size
        ), padding=self.kernel_size//2) * self.scaling

class MultiCharacterDataset(Dataset):
    """Dataset for multi-character training"""

    def __init__(self, data_root: str, tokenizer: CLIPTokenizer, tokenizer_2: CLIPTokenizer, size: int = 1024):
        self.data_root = Path(data_root)
        self.tokenizer = tokenizer
        self.tokenizer_2 = tokenizer_2
        self.size = size
        self.images = []
        self.captions = []
        
        # Load data from character folders
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]

        for char_folder in self.data_root.iterdir():
            if char_folder.is_dir():
                char_name = char_folder.name
                print(f"Processing character folder: {char_name}")

                # Load images and captions for all supported formats
                char_images_count = 0
                for ext in image_extensions:
                    for img_file in char_folder.glob(ext):
                        # Look for corresponding caption file
                        caption_file = img_file.with_suffix(".txt")
                        if caption_file.exists():
                            try:
                                with open(caption_file, 'r', encoding='utf-8') as f:
                                    caption = f.read().strip()

                                # Add character token to caption
                                caption = f"a photo of {char_name}, {caption}"

                                self.images.append(str(img_file))
                                self.captions.append(caption)
                                char_images_count += 1
                            except Exception as e:
                                print(f"Warning: Could not read caption file {caption_file}: {e}")
                        else:
                            print(f"Warning: No caption file found for {img_file}")

                print(f"  Found {char_images_count} images with captions for {char_name}")

        print(f"Loaded {len(self.images)} images from {len(list(self.data_root.iterdir()))} characters")

        # Check if dataset is empty
        if len(self.images) == 0:
            raise ValueError(
                f"No images found in {self.data_root}. "
                f"Make sure your data structure is: data_root/character_name/image.ext and data_root/character_name/image.txt "
                f"Supported image formats: {', '.join(image_extensions)}"
            )
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        # Load and preprocess image
        image_path = self.images[idx]
        image = Image.open(image_path).convert("RGB")
        
        # Resize and center crop
        image = image.resize((self.size, self.size), Image.LANCZOS)
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image).permute(2, 0, 1)
        
        # Normalize to [-1, 1]
        image = (image - 0.5) / 0.5
        
        # Tokenize caption for both text encoders
        caption = self.captions[idx]
        text_inputs = self.tokenizer(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        text_inputs_2 = self.tokenizer_2(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        return {
            "pixel_values": image,
            "input_ids": text_inputs.input_ids.squeeze(),
            "attention_mask": text_inputs.attention_mask.squeeze(),
            "input_ids_2": text_inputs_2.input_ids.squeeze(),
            "attention_mask_2": text_inputs_2.attention_mask.squeeze()
        }

class LoRATrainer:
    """Multi-Character LoRA Trainer"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.accelerator = Accelerator(
            gradient_accumulation_steps=config.get("gradient_accumulation_steps", 1),
            mixed_precision=config.get("mixed_precision", "fp16")
        )
        
        # Set random seed
        if config.get("seed"):
            set_seed(config["seed"])
        
        self.setup_models()
        self.setup_lora_layers()
        
    def setup_models(self):
        """Initialize SDXL models"""
        model_id = self.config.get("model_id", "stabilityai/stable-diffusion-xl-base-1.0")

        # Determine dtype based on mixed precision setting
        model_dtype = torch.float32 if self.config.get("mixed_precision") == "no" else torch.float16

        # Load tokenizers
        self.tokenizer = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer"
        )
        self.tokenizer_2 = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer_2"
        )

        # Load text encoders (SDXL has two)
        self.text_encoder = CLIPTextModel.from_pretrained(
            model_id, subfolder="text_encoder", torch_dtype=model_dtype
        )
        self.text_encoder_2 = CLIPTextModelWithProjection.from_pretrained(
            model_id, subfolder="text_encoder_2", torch_dtype=model_dtype
        )

        # Load UNet
        self.unet = UNet2DConditionModel.from_pretrained(
            model_id, subfolder="unet", torch_dtype=model_dtype
        )

        # Load VAE
        self.vae = AutoencoderKL.from_pretrained(
            model_id, subfolder="vae", torch_dtype=model_dtype
        )

        # Freeze original parameters
        self.text_encoder.requires_grad_(False)
        self.text_encoder_2.requires_grad_(False)
        self.unet.requires_grad_(False)
        self.vae.requires_grad_(False)
        
    def setup_lora_layers(self):
        """Setup LoRA layers for UNet"""
        self.lora_layers = {}
        rank = self.config.get("lora_rank", 4)
        alpha = self.config.get("lora_alpha", 32)

        # Add LoRA to attention layers
        for name, module in self.unet.named_modules():
            if "attn" in name and isinstance(module, torch.nn.Linear):
                # Skip if already has LoRA
                if hasattr(module, 'lora_A'):
                    continue

                # Create LoRA layer with same dtype as the module
                lora_layer = LoRALinearLayer(
                    module.in_features,
                    module.out_features,
                    rank=rank,
                    alpha=alpha
                )

                # Convert LoRA layer to same dtype as the original module
                lora_layer = lora_layer.to(dtype=module.weight.dtype)

                # Register as buffer to avoid issues
                self.lora_layers[name] = lora_layer
                
                # Hook to add LoRA output to original
                def make_hook(lora_layer):
                    def hook(_, input, output):
                        # Ensure input and output have consistent dtype
                        input_tensor = input[0]
                        lora_output = lora_layer(input_tensor)
                        # Ensure lora_output matches output dtype
                        lora_output = lora_output.to(output.dtype)
                        return output + lora_output
                    return hook
                
                module.register_forward_hook(make_hook(lora_layer))
        
        # Collect trainable parameters
        self.trainable_params = []
        for lora_layer in self.lora_layers.values():
            self.trainable_params.extend(lora_layer.parameters())
        
        print(f"Added LoRA to {len(self.lora_layers)} layers")
        print(f"Trainable parameters: {sum(p.numel() for p in self.trainable_params):,}")

    def encode_prompt(self, input_ids, input_ids_2, attention_mask, attention_mask_2):
        """Encode prompts using both SDXL text encoders"""
        # Encode with first text encoder
        text_encoder_output = self.text_encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )
        prompt_embeds = text_encoder_output.last_hidden_state

        # Encode with second text encoder
        text_encoder_2_output = self.text_encoder_2(
            input_ids=input_ids_2,
            attention_mask=attention_mask_2,
            return_dict=True
        )
        prompt_embeds_2 = text_encoder_2_output.last_hidden_state
        pooled_prompt_embeds = text_encoder_2_output.text_embeds

        # Concatenate the embeddings
        prompt_embeds = torch.cat([prompt_embeds, prompt_embeds_2], dim=-1)

        return prompt_embeds, pooled_prompt_embeds

    def train(self, train_dataloader):
        """Training loop"""
        # Setup optimizer
        optimizer = torch.optim.AdamW(
            self.trainable_params,
            lr=self.config.get("learning_rate", 1e-4),
            betas=(0.9, 0.999),
            weight_decay=self.config.get("weight_decay", 0.01),
            eps=1e-08
        )
        
        # Setup scheduler
        num_training_steps = len(train_dataloader) * self.config.get("num_epochs", 10)
        lr_scheduler = get_scheduler(
            self.config.get("lr_scheduler", "cosine"),
            optimizer=optimizer,
            num_warmup_steps=self.config.get("warmup_steps", 500),
            num_training_steps=num_training_steps
        )
        
        # Prepare for distributed training
        lora_modules = list(self.lora_layers.values())
        optimizer, train_dataloader, lr_scheduler = self.accelerator.prepare(
            optimizer, train_dataloader, lr_scheduler
        )
        
        # Move models to device
        self.unet.to(self.accelerator.device)
        self.text_encoder.to(self.accelerator.device)
        self.text_encoder_2.to(self.accelerator.device)
        self.vae.to(self.accelerator.device)
        
        for lora_layer in lora_modules:
            lora_layer.to(self.accelerator.device)
        
        # Training loop
        global_step = 0
        
        for epoch in range(self.config.get("num_epochs", 10)):
            for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1}"):
                with self.accelerator.accumulate(self.unet):
                    # Encode images to latent space
                    pixel_values = batch["pixel_values"].to(self.accelerator.device, dtype=self.vae.dtype)
                    latents = self.vae.encode(pixel_values).latent_dist.sample()
                    latents = latents * 0.18215

                    # Sample noise with same dtype as latents
                    noise = torch.randn_like(latents, dtype=latents.dtype, device=latents.device)
                    bsz = latents.shape[0]

                    # Sample timesteps
                    timesteps = torch.randint(
                        0, 1000, (bsz,), device=latents.device
                    ).long()

                    # Add noise to latents
                    noisy_latents = self.add_noise(latents, noise, timesteps)

                    # Encode text with both encoders
                    input_ids = batch["input_ids"].to(self.accelerator.device)
                    input_ids_2 = batch["input_ids_2"].to(self.accelerator.device)
                    attention_mask = batch["attention_mask"].to(self.accelerator.device)
                    attention_mask_2 = batch["attention_mask_2"].to(self.accelerator.device)

                    prompt_embeds, pooled_prompt_embeds = self.encode_prompt(
                        input_ids, input_ids_2, attention_mask, attention_mask_2
                    )

                    # Ensure embeddings have correct dtype
                    prompt_embeds = prompt_embeds.to(dtype=noisy_latents.dtype)
                    pooled_prompt_embeds = pooled_prompt_embeds.to(dtype=noisy_latents.dtype)

                    # Create time_ids for SDXL (original_size, crops_coords_top_left, target_size)
                    original_size = (self.config.get("resolution", 1024), self.config.get("resolution", 1024))
                    crops_coords_top_left = (0, 0)
                    target_size = (self.config.get("resolution", 1024), self.config.get("resolution", 1024))

                    time_ids = torch.tensor([original_size + crops_coords_top_left + target_size],
                                          dtype=noisy_latents.dtype, device=self.accelerator.device)
                    time_ids = time_ids.repeat(bsz, 1)

                    # Create added_cond_kwargs for SDXL
                    added_cond_kwargs = {
                        "text_embeds": pooled_prompt_embeds,
                        "time_ids": time_ids
                    }

                    # Predict noise
                    noise_pred = self.unet(
                        noisy_latents,
                        timesteps,
                        encoder_hidden_states=prompt_embeds,
                        added_cond_kwargs=added_cond_kwargs
                    ).sample

                    # Compute loss (ensure both tensors have same dtype)
                    noise_pred = noise_pred.to(dtype=noise.dtype)
                    loss = F.mse_loss(noise_pred, noise, reduction="mean")
                    
                    # Backward pass
                    self.accelerator.backward(loss)
                    
                    if self.accelerator.sync_gradients:
                        self.accelerator.clip_grad_norm_(self.trainable_params, 1.0)
                    
                    optimizer.step()
                    lr_scheduler.step()
                    optimizer.zero_grad()
                
                if self.accelerator.sync_gradients:
                    global_step += 1
                    
                    # Log progress
                    if global_step % 100 == 0:
                        logger.info(f"Step {global_step}, Loss: {loss.item():.4f}")
                    
                    # Save checkpoint
                    if global_step % self.config.get("save_steps", 1000) == 0:
                        self.save_checkpoint(global_step)
        
        # Final save
        self.save_checkpoint("final")
    
    def add_noise(self, latents, noise, timesteps):
        """Add noise to latents according to timestep"""
        # Simplified noise scheduler
        alphas = torch.linspace(0.9999, 0.0001, 1000, dtype=latents.dtype, device=latents.device)
        alphas_cumprod = torch.cumprod(alphas, dim=0)

        sqrt_alpha_prod = alphas_cumprod[timesteps] ** 0.5
        sqrt_one_minus_alpha_prod = (1 - alphas_cumprod[timesteps]) ** 0.5

        sqrt_alpha_prod = sqrt_alpha_prod.flatten()
        while len(sqrt_alpha_prod.shape) < len(latents.shape):
            sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)

        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()
        while len(sqrt_one_minus_alpha_prod.shape) < len(latents.shape):
            sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)

        # Ensure all tensors have the same dtype and device
        sqrt_alpha_prod = sqrt_alpha_prod.to(dtype=latents.dtype, device=latents.device)
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.to(dtype=latents.dtype, device=latents.device)
        noise = noise.to(dtype=latents.dtype, device=latents.device)

        return sqrt_alpha_prod * latents + sqrt_one_minus_alpha_prod * noise
    
    def save_checkpoint(self, step):
        """Save LoRA weights"""
        save_dir = Path(self.config["output_dir"]) / f"checkpoint-{step}"
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Save LoRA weights
        lora_state_dict = {}
        for name, lora_layer in self.lora_layers.items():
            lora_state_dict[f"{name}.lora_A"] = lora_layer.lora_A.data
            lora_state_dict[f"{name}.lora_B"] = lora_layer.lora_B.data
        
        torch.save(lora_state_dict, save_dir / "lora_weights.pt")
        
        # Save config
        with open(save_dir / "config.json", "w") as f:
            json.dump(self.config, f, indent=2)
        
        logger.info(f"Saved checkpoint to {save_dir}")

def main():
    parser = argparse.ArgumentParser(description="Train multi-character LoRA")
    parser.add_argument("--data_root", type=str, required=True, help="Root directory containing character folders")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for checkpoints")
    parser.add_argument("--model_id", type=str, default="stabilityai/stable-diffusion-xl-base-1.0")
    parser.add_argument("--resolution", type=int, default=1024)
    parser.add_argument("--batch_size", type=int, default=2)
    parser.add_argument("--num_epochs", type=int, default=10)
    parser.add_argument("--learning_rate", type=float, default=1e-4)
    parser.add_argument("--lora_rank", type=int, default=4)
    parser.add_argument("--lora_alpha", type=float, default=32)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--mixed_precision", type=str, default="auto", choices=["no", "fp16", "bf16", "auto"],
                        help="Mixed precision mode. 'auto' will use 'no' for MPS, 'fp16' for CUDA. Use 'no' if you encounter dtype errors")
    parser.add_argument("--force_fp32", action="store_true",
                        help="Force all operations to use fp32 (disables mixed precision completely)")
    
    args = parser.parse_args()
    
    # Create config
    config = {
        "data_root": args.data_root,
        "output_dir": args.output_dir,
        "model_id": args.model_id,
        "resolution": args.resolution,
        "batch_size": args.batch_size,
        "num_epochs": args.num_epochs,
        "learning_rate": args.learning_rate,
        "lora_rank": args.lora_rank,
        "lora_alpha": args.lora_alpha,
        "seed": args.seed,
        "gradient_accumulation_steps": 2,
        "save_steps": 500,
        "warmup_steps": 100,
        "mixed_precision": (
            "no" if args.force_fp32 or (args.mixed_precision == "auto" and torch.backends.mps.is_available())
            else "fp16" if args.mixed_precision == "auto" and torch.cuda.is_available()
            else "no" if args.mixed_precision == "auto"
            else args.mixed_precision
        )
    }
    
    # Setup dataset
    tokenizer = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer")
    tokenizer_2 = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer_2")

    dataset = MultiCharacterDataset(
        args.data_root,
        tokenizer,
        tokenizer_2,
        size=args.resolution
    )

    # Check if dataset has any samples
    if len(dataset) == 0:
        print("ERROR: Dataset is empty! Please check your data directory structure.")
        print(f"Expected structure: {args.data_root}/character_name/image.ext and {args.data_root}/character_name/image.txt")
        return

    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    # Create trainer and start training
    trainer = LoRATrainer(config)
    trainer.train(dataloader)
    
    print("Training completed!")

if __name__ == "__main__":
    main()
