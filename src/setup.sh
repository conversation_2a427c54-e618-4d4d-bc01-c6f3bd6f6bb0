#!/bin/bash
# RunPod Environment Setup for Multi-Character LoRA Training
# Run this script first to set up your RunPod environment

set -e

echo "🚀 Setting up RunPod environment for Multi-Character LoRA training..."

# Update system
echo "📦 Updating system packages..."
apt-get update -qq
apt-get install -y git wget curl unzip

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Download the training scripts (save the Python scripts from above as files)
echo "📄 Setting up training scripts..."

# Check GPU
echo "🔍 Checking GPU availability..."
python3 -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}'); print(f'Current device: {torch.cuda.current_device() if torch.cuda.is_available() else \"CPU\"}')"

echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Create your data directory structure:"
echo "   /workspace/lora_training/data/"
echo "   ├── character1/"
echo "   │   ├── image1.jpg"
echo "   │   ├── image1.txt"
echo "   │   ├── image2.jpg"
echo "   │   └── image2.txt"
echo "   └── character2/"
echo "       ├── image1.jpg"
echo "       ├── image1.txt"
echo "       └── ..."
echo ""
echo "2. Run training:"
echo "   python train_lora.py --data_root ./data --output_dir ./outputs"
echo ""
echo "3. Test the model:"
echo "   python test_lora.py --lora_path ./outputs/checkpoint-final --test_characters character1 character2"